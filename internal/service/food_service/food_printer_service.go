package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

// FoodPrinterService 美食打印机关联业务逻辑
type FoodPrinterService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// GetMerchantFoodPrinters 返回商户的美食打印机关联列表
func (serv *FoodPrinterService) GetMerchantFoodPrinters(ctx context.Context, merchantNo string) ([]*model.FoodPrinterModel, error) {
	var list []*model.FoodPrinterModel
	return list, util.GetDB(ctx, serv.DB).Where("merchant_no =?", merchantNo).Find(&list).Error
}

// SyncPrinterFoods 同步打印机菜品信息 (本地  ===》 云端)
func (serv *FoodPrinterService) SyncPrinterFoods(ctx context.Context, merchantNo, printerID string, foods []*model.FoodPrinterModel) error {

	err := serv.Trans.Exec(ctx, func(ctx context.Context) error {
		db := util.GetDB(ctx, serv.DB)
		// 删除旧的菜品
		err := db.Where("merchant_no =? AND printer_id = ?", merchantNo, printerID).
			Delete(&model.FoodPrinterModel{}).Error
		if err != nil {
			return err
		}
		// 保存新的菜品
		err = db.Create(&foods).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
