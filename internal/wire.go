package internal

import (
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http"
	v2 "ros-api-go/internal/http/controller/v2"
	"ros-api-go/internal/http/controller/v2/foods"
	"ros-api-go/internal/http/controller/v2/local"
	merchant "ros-api-go/internal/http/controller/v2/merchant"
	orderV2 "ros-api-go/internal/http/controller/v2/order"
	"ros-api-go/internal/http/controller/v2/pay"
	"ros-api-go/internal/http/controller/v2/scan"
	"ros-api-go/internal/http/controller/v2/statistics"
	"ros-api-go/internal/http/controller/v2/sync"
	"ros-api-go/internal/http/middleware"
	"ros-api-go/internal/route"
	"ros-api-go/internal/service"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/internal/service/scan_service"
	"ros-api-go/internal/service/statistic_service"

	"github.com/google/wire"
)

// AppSet 应用依赖注入
var AppSet = wire.NewSet(
	wire.Struct(new(App), "*"),
	wire.Struct(new(http.Casbinx), "*"),
	wire.Struct(new(middleware.AuthProvider), "*"),
	wire.Struct(new(middleware.ServerAuthProvider), "*"),
	wire.Struct(new(middleware.WechatUserProvider), "*"),
	wire.Struct(new(middleware.CasbinProvider), "*"),
	wire.Struct(new(middleware.CheckUserMerchantProvider), "*"),
	wire.Struct(new(Routers), "*"),
	wire.Struct(new(route.SyncRouterGroup), "*"),
	wire.Struct(new(route.ApiRouterGroup), "*"),
	wire.Struct(new(route.PayRouterGroup), "*"),
	wire.Struct(new(route.LocalRouterGroup), "*"),
	wire.Struct(new(route.ScanRouterGroup), "*"),
	wire.Struct(new(route.AdminRouterGroup), "*"),
	wire.Struct(new(route.MerchantRouterGroup), "*"),
	wire.Struct(new(route.CloudRouteGroup), "*"),
	wire.Struct(new(route.FoodRouterGroup), "*"),
)

// BizSet 业务依赖注入
var BizSet = wire.NewSet(
	// Auth部分
	wire.Struct(new(service.AuthService), "*"),
	wire.Struct(new(v2.AuthController), "*"),
	wire.Struct(new(v2.OperationPasswordController), "*"),
	// 权限部分
	wire.Struct(new(service.PermissionService), "*"),
	wire.Struct(new(sync.PermissionController), "*"),

	// 本地服务
	wire.Struct(new(service.LocalServerService), "*"),
	// 商家信息
	wire.Struct(new(service.MerchantService), "*"),
	// 餐厅区域
	wire.Struct(new(service.AreaService), "*"),
	wire.Struct(new(sync.AreaController), "*"),
	// 餐厅餐桌
	wire.Struct(new(service.TableService), "*"),
	wire.Struct(new(sync.TableController), "*"),
	// 打印机
	wire.Struct(new(service.PrinterService), "*"),
	wire.Struct(new(sync.PrinterController), "*"),
	wire.Struct(new(v2.PrinterController), "*"),
	// 菜品打印机
	wire.Struct(new(food_service.FoodPrinterService), "*"),
	wire.Struct(new(sync.FoodPrinterController), "*"),
	// 菜品分类
	wire.Struct(new(food_service.FoodCategoryService), "*"),
	wire.Struct(new(sync.FoodCategory), "*"),
	// 菜品
	wire.Struct(new(food_service.FoodsService), "*"),
	wire.Struct(new(sync.FoodsController), "*"),
	// 菜品套餐
	wire.Struct(new(food_service.FoodComboService), "*"),
	wire.Struct(new(sync.FoodComboController), "*"),
	// 商家信息更新时间记录
	wire.Struct(new(service.MerchantInfoUpdateService), "*"),
	wire.Struct(new(sync.MerchantInfoUpdateController), "*"),
	// 商家备注
	wire.Struct(new(service.RemarkService), "*"),
	wire.Struct(new(sync.RemarkController), "*"),
	// 商家备注分类
	wire.Struct(new(service.RemarkCategoryService), "*"),
	wire.Struct(new(sync.RemarkCategoryController), "*"),
	// 商家用户
	wire.Struct(new(service.UserService), "*"),
	wire.Struct(new(sync.UserController), "*"),
	// 商家配置
	wire.Struct(new(service.MerchantConfigService), "*"),
	wire.Struct(new(sync.MerchantConfigController), "*"),
	// 商家角色
	wire.Struct(new(service.MerchantRoleService), "*"),
	wire.Struct(new(sync.MerchantRoleController), "*"),
	// 支付方式
	wire.Struct(new(service.PaymentTypeService), "*"),
	wire.Struct(new(sync.PaymentTypeController), "*"),
	// 订单支付
	wire.Struct(new(service.PaymentService), "*"),
	wire.Struct(new(service.WechatPayService), "*"),
	wire.Struct(new(service.AlipayService), "*"),
	wire.Struct(new(service.PaymentLogService), "*"),
	wire.Struct(new(service.RefundLogService), "*"),
	wire.Struct(new(pay.PaymentController), "*"),
	wire.Struct(new(pay.WechatPayController), "*"),
	wire.Struct(new(pay.AliPayController), "*"),
	wire.Struct(new(pay.CustomerPayController), "*"),
	wire.Struct(new(pay.PaymentReverseController), "*"),
	wire.Struct(new(pay.PaymentTypeController), "*"),

	// VIP充值
	wire.Struct(new(service.RechargeOrderService), "*"),
	wire.Struct(new(pay.RechargeController), "*"),

	// 订单信息同步
	wire.Struct(new(service.OrderSyncService), "*"),
	wire.Struct(new(sync.OrderSyncController), "*"),
	// print controller
	wire.Struct(new(local.PrinterController), "*"),

	// websocket 通知转发
	wire.Struct(new(local.BroadcastController), "*"),

	// RPC 收银端方法调用
	wire.Struct(new(v2.RPCInvokeController), "*"),

	// 菜品/分类
	wire.Struct(new(foods.FoodCategoryController), "*"),
	wire.Struct(new(foods.FoodController), "*"),

	// 订单
	wire.Struct(new(service.OrderService), "*"),
	wire.Struct(new(service.OrderCloudService), "*"),
	wire.Struct(new(service.PrintService), "*"),
	wire.Struct(new(service.OrderDetailService), "*"),
	wire.Struct(new(service.RefundBatchService), "*"),
	wire.Struct(new(service.RefundOrderLogService), "*"),
	wire.Struct(new(service.ChangeOrderLogService), "*"),
	wire.Struct(new(orderV2.OrderRefundController), "*"),
	wire.Struct(new(orderV2.OrderController), "*"),
	wire.Struct(new(orderV2.OrderPaymentController), "*"),
	wire.Struct(new(orderV2.PaymentReverseController), "*"),
	wire.Struct(new(v2.UserController), "*"),

	// 会员相关
	wire.Struct(new(service.CustomerService), "*"),
	wire.Struct(new(service.CustomerConsumptionLogService), "*"),

	// Mqtt 相关
	// MqttAcl
	wire.Struct(new(service.MqttAclService), "*"),
	wire.Struct(new(v2.MqttController), "*"),

	// MerchantRoleController
	wire.Struct(new(service.MerchantEmployeeService), "*"),
	wire.Struct(new(merchant.RoleController), "*"),
	wire.Struct(new(merchant.EmployeeController), "*"),

	// permission controller
	wire.Struct(new(v2.PermissionController), "*"),

	// Bill Controller
	wire.Struct(new(service.BillService), "*"),
	wire.Struct(new(v2.BillController), "*"),

	// Terminal Controller
	wire.Struct(new(service.TerminalService), "*"),
	wire.Struct(new(v2.TerminalController), "*"),

	// DebtHolder Controller
	wire.Struct(new(service.DebtHolderService), "*"),
	wire.Struct(new(v2.DebtHolderController), "*"),

	// DebtTransaction Controller
	wire.Struct(new(service.DebtTransactionService), "*"),
	wire.Struct(new(v2.DebtTransactionController), "*"),

	// DebtRepaymentOrder Controller
	wire.Struct(new(service.DebtRepaymentOrderService), "*"),
	wire.Struct(new(pay.DebtRepaymentController), "*"),
	wire.Struct(new(pay.DebtPayController), "*"),

	// 员工交班
	wire.Struct(new(service.ShiftService), "*"),
	wire.Struct(new(service.HandoverService), "*"),
	wire.Struct(new(v2.HandoverController), "*"),

	// Handlers
	wire.Struct(new(handler.RefundHandler), "*"),             // 退款处理器
	wire.Struct(new(handler.RechargeHandler), "*"),           // 充值处理器
	wire.Struct(new(handler.RpcHandler), "*"),                // RPC 请求处理器
	wire.Struct(new(handler.PaymentHandler), "*"),            // 扫码点菜处理器
	wire.Struct(new(handler.OrderSyncHandler), "*"),          // 云订单同步处理器
	wire.Struct(new(handler.MerchantInfoUpdateHandler), "*"), // 商家信息更新处理器
	wire.Struct(new(handler.DebtRepaymentHandler), "*"),      // 寡占还款处理器

	// 统计信息
	wire.Struct(new(statistic_service.BusinessStatisticService), "*"),
	wire.Struct(new(statistic_service.FoodsStatisticService), "*"),
	wire.Struct(new(statistic_service.HandoverStatisticService), "*"),
	wire.Struct(new(statistic_service.GraphStatisticService), "*"),
	wire.Struct(new(statistic_service.MasterStatisticService), "*"),
	wire.Struct(new(statistics.BusinessStatisticController), "*"),
	wire.Struct(new(statistics.FoodsStatisticController), "*"),
	wire.Struct(new(statistics.HandoverStatisticController), "*"),
	wire.Struct(new(statistics.GraphStatisticController), "*"),
	wire.Struct(new(statistics.MasterStatisticController), "*"),

	// 扫码点菜
	wire.Struct(new(service.WechatUserService), "*"),
	wire.Struct(new(scan_service.ScanOrderService), "*"),
	wire.Struct(new(scan_service.MerchantServiceService), "*"),
	wire.Struct(new(scan.WechatAuthController), "*"),
	wire.Struct(new(scan.ScanOrderController), "*"),
	wire.Struct(new(scan.ServiceController), "*"),
)
