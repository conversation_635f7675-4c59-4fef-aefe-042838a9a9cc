package request

type FoodSellClearDataRequest struct {
	MerchantNo     string  `json:"-"`                                       // 商户号
	ID             int64   `json:"id" binding:"required,min=1"`             // 菜品ID
	CellClearState bool    `json:"cell_clear_state" binding:"boolean"`      // 沽清状态：1-有库存，0-无库存
	SellClearCount float64 `json:"sell_clear_count" binding:"number,min=0"` // 售清数量
	RemainingCount float64 `json:"remaining_count" binding:"number,min=0"`  // 剩余库存数量
}

type CancelFood struct {
	FoodsCount float64 `json:"foods_count"`
	Remarks    *string `json:"remarks"`
}

type CancelAllFoodRequest struct {
	Remarks *string `json:"remarks"`
}

// FoodListRequest 美食列表请求参数
type FoodListRequest struct {
	MerchantNo       string `form:"-"`                  // 商户号
	FoodCategoryID   int64  `form:"food_category_id"`   // 分类ID
	Keyword          string `form:"keyword"`            // 关键词
	State            *int   `form:"state"`              // 状态
	SellClearAll     string `form:"sell_clear_all"`     // 是否清理所有销售
	SupportScanOrder *bool  `form:"support_scan_order"` // 是否支持扫码点单
}

// ComboFoodRequest 套餐菜品请求参数
type ComboFoodRequest struct {
	ComboFoodID string  `json:"combo_food_id" binding:"required"`     // 套餐菜品ID（逗号分隔）
	ComboPrice  float64 `json:"combo_price" binding:"required,min=0"` // 套餐价格
	ComboCount  int     `json:"combo_count" binding:"required,min=1"` // 套餐数量
	NameUg      string  `json:"name_ug" binding:"required"`           // 名称(维语)
	NameZh      string  `json:"name_zh" binding:"required"`           // 名称(中文)
}

// CreateFoodRequest 创建美食请求参数
type CreateFoodRequest struct {
	FoodCategoryID   int64              `json:"food_category_id" binding:"required,min=1"` // 分类ID
	Image            string             `json:"image"`                                     // 图片
	ShortcutCode     string             `json:"shortcut_code"`                             // 快捷码
	NameUg           string             `json:"name_ug" binding:"required"`                // 名称(维语)
	NameZh           string             `json:"name_zh" binding:"required"`                // 名称(中文)
	CostPrice        float64            `json:"cost_price" binding:"required,min=0"`       // 定价
	VipPrice         float64            `json:"vip_price" binding:"required,min=0"`        // 会员价
	Price            float64            `json:"price" binding:"required,min=0"`            // 现价
	FormatID         int                `json:"format_id" binding:"required,min=1"`        // 规格ID
	IsSpecialFood    bool               `json:"is_special_food"`                           // 是否特色菜
	SupportScanOrder bool               `json:"support_scan_order"`                        // 是否支持扫码点单
	CellClearState   bool               `json:"cell_clear_state"`                          // 是否设置剩余数量
	SellClearCount   float64            `json:"sell_clear_count" binding:"min=0"`          // 美食剩余限量数
	RemainingCount   float64            `json:"remaining_count" binding:"min=0"`           // 美食剩余数量
	IsCombo          bool               `json:"is_combo"`                                  // 是否套餐菜
	Sort             int64              `json:"sort"`                                      // 排序
	State            int64              `json:"state" binding:"min=0,max=1"`               // 状态
	ComboFoods       []ComboFoodRequest `json:"combo_foods"`                               // 套餐菜品列表
}

// UpdateFoodRequest 更新美食请求参数
type UpdateFoodRequest struct {
	FoodCategoryID   int64              `json:"food_category_id" binding:"required,min=1"` // 分类ID
	Image            string             `json:"image"`                                     // 图片
	ShortcutCode     string             `json:"shortcut_code"`                             // 快捷码
	NameUg           string             `json:"name_ug" binding:"required"`                // 名称(维语)
	NameZh           string             `json:"name_zh" binding:"required"`                // 名称(中文)
	CostPrice        float64            `json:"cost_price" binding:"required,min=0"`       // 定价
	VipPrice         float64            `json:"vip_price" binding:"required,min=0"`        // 会员价
	Price            float64            `json:"price" binding:"required,min=0"`            // 现价
	FormatID         int                `json:"format_id" binding:"required,min=1"`        // 规格ID
	IsSpecialFood    bool               `json:"is_special_food"`                           // 是否特色菜
	SupportScanOrder bool               `json:"support_scan_order"`                        // 是否支持扫码点单
	CellClearState   bool               `json:"cell_clear_state"`                          // 是否设置剩余数量
	SellClearCount   float64            `json:"sell_clear_count" binding:"min=0"`          // 美食剩余限量数
	RemainingCount   float64            `json:"remaining_count" binding:"min=0"`           // 美食剩余数量
	IsCombo          bool               `json:"is_combo"`                                  // 是否套餐菜
	Sort             int64              `json:"sort"`                                      // 排序
	State            int64              `json:"state" binding:"min=0,max=1"`               // 状态
	ComboFoods       []ComboFoodRequest `json:"combo_foods"`                               // 套餐菜品列表
}
