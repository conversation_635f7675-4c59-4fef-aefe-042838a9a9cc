package request

import "ros-api-go/pkg/util"

// CreateFoodCategoryRequest 创建菜品分类请求
type CreateFoodCategoryRequest struct {
	NameUg string `json:"name_ug" binding:"required"`          // 名称(维语)
	NameZh string `json:"name_zh" binding:"required"`          // 名称(中文)
	Sort   int64  `json:"sort" binding:"required"`             // 排序
	Type   int64  `json:"type" binding:"required,oneof=1 2 3"` // 分类类型: 1美食分类 2 加料分类 3 饭盒
	State  int64  `json:"state" binding:"required,oneof=0 1"`  // 状态：0-禁用 1-启用
}

// UpdateFoodCategoryRequest 更新菜品分类请求
type UpdateFoodCategoryRequest struct {
	NameUg string `json:"name_ug" binding:"required"`         // 名称(维语)
	NameZh string `json:"name_zh" binding:"required"`         // 名称(中文)
	Sort   int64  `json:"sort" binding:"required"`            // 排序
	State  int64  `json:"state" binding:"required,oneof=0 1"` // 状态：0-禁用 1-启用
}

// UpdateFoodCategoryStateRequest 更新菜品分类状态请求
type UpdateFoodCategoryStateRequest struct {
	State int64 `json:"state" binding:"required,oneof=0 1"` // 状态：0-禁用 1-启用
}

// FoodCategoryListRequest 获取菜品分类列表请求
type FoodCategoryListRequest struct {
	util.PaginationParam
	MerchantNo string `form:"merchant_no"` // 商户编号
	Type       *int64 `form:"type"`        // 分类类型过滤: 1美食分类 2 加料分类 3 饭盒
	State      *int64 `form:"state"`       // 状态过滤：0-禁用 1-启用
}
