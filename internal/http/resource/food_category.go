package resource

import (
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type FoodCategoryResource struct {
	ID         int64   `json:"id"`          // Unique ID
	MerchantNo string  `json:"merchant_no"` // 商家编号
	Type       int64   `json:"type"`        // 分类类型: 1美食分类 2 加料分类 3 饭盒
	NameUg     string  `json:"name_ug"`     // 名称(维语)
	NameZh     string  `json:"name_zh"`     // 名称(中文)
	Sort       int64   `json:"sort"`        // 排序
	State      int64   `json:"state"`       // 状态
	FoodsCount int     `json:"foods_count"` // 菜品数量
	CreatedAt  *string `json:"created_at"`  // Create time
	UpdatedAt  *string `json:"updated_at"`  // Update time
	DeletedAt  *string `json:"deleted_at"`  // Delete time
}

func (rc *FoodCategoryResource) Make(item *model.FoodCategoryModel) *FoodCategoryResource {
	if item == nil {
		return nil
	}
	data := FoodCategoryResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		Type:       item.Type,
		NameUg:     item.NameUg,
		NameZh:     item.NameZh,
		Sort:       item.Sort,
		State:      item.State,
		FoodsCount: item.FoodsCount,
		CreatedAt:  util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(&item.UpdatedAt),
		DeletedAt:  util.FormatDateTime(&item.DeletedAt.Time),
	}
	return &data
}

func (rc *FoodCategoryResource) Collection(items []*model.FoodCategoryModel) []*FoodCategoryResource {

	if items == nil || len(items) == 0 {
		return []*FoodCategoryResource{}
	}
	data := make([]*FoodCategoryResource, len(items))
	for i, item := range items {
		data[i] = rc.Make(item)
	}
	return data
}
