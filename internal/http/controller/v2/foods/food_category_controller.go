package foods

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service/food_service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"strconv"
)

type FoodCategoryController struct {
	FoodCategoryService       food_service.FoodCategoryService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

// GetAvailableFoodCategories 获取菜品分类列表
//
// @Tags 菜品分类
// @Security ApiAuthToken
// @Summary 获取菜品分类列表
// @Success 200 {object} util.ResponseResult{Data=[]resource.FoodCategoryResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /cloud/api/v2/foodCategories [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:54"]
// @X-Version ["2.0"]
func (ctrl *FoodCategoryController) GetAvailableFoodCategories(c *gin.Context) {
	ctx := c.Request.Context()
	MerchantNo := util.GetMerchantNo(c)
	catType := util.StrToInt64(c.Query("type"))
	if catType == 0 {
		catType = model.FoodCategoryTypeFood // 默认为美食分类
	}
	result, err := ctrl.FoodCategoryService.GetAvailableFoodCategories(ctx, MerchantNo, catType)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodCategoryResource{}).Collection(result))
}

// GetFoodCategoryList 获取菜品分类列表（分页）
//
// @Tags 菜品分类
// @Security ApiAuthToken
// @Summary 获取菜品分类列表（分页）
// @Success 200 {object} util.ResponseResult{Data=util.PaginationResult{data=[]resource.FoodCategoryResource}}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodCategories [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:54"]
// @X-Version ["2.0"]
func (ctrl *FoodCategoryController) GetFoodCategoryList(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req request.FoodCategoryListRequest
	if err := util.ParseQuery(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	req.MerchantNo = merchantNo
	result, pageResult, err := ctrl.FoodCategoryService.GetList(ctx, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResPage(c, (&resource.FoodCategoryResource{}).Collection(result), pageResult)
}

// GetFoodCategory 获取单个菜品分类
//
// @Tags 菜品分类
// @Security ApiAuthToken
// @Summary 获取单个菜品分类
// @Success 200 {object} util.ResponseResult{ Data=resource.FoodCategoryResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodCategories/{id} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:55"]
// @X-Version ["2.0"]
func (ctrl *FoodCategoryController) GetFoodCategory(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	result, err := ctrl.FoodCategoryService.GetByID(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodCategoryResource{}).Make(result))
}

// CreateFoodCategory 创建菜品分类
//
// @Tags 菜品分类
// @Security ApiAuthToken
// @Summary  创建菜品分类
// @Param request body request.CreateFoodCategoryRequest true "创建菜品分类请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{ Data=resource.FoodCategoryResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodCategories [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:55"]
// @X-Version ["2.0"]
func (ctrl *FoodCategoryController) CreateFoodCategory(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req request.CreateFoodCategoryRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	result, err := ctrl.FoodCategoryService.Create(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodCategoryResource{}).Make(result))
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, model.FoodModel{})
}

// UpdateFoodCategory 更新菜品分类
//
// @Tags 菜品分类
// @Security ApiAuthToken
// @Summary 更新菜品分类
// @Param id path int true "菜品分类ID"
// @Param request body request.UpdateFoodCategoryRequest true "更新菜品分类请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{ Data=resource.FoodCategoryResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodCategories/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:55"]
// @X-Version ["2.0"]
func (ctrl *FoodCategoryController) UpdateFoodCategory(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	var req request.UpdateFoodCategoryRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	result, err := ctrl.FoodCategoryService.Update(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodCategoryResource{}).Make(result))
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, model.FoodModel{})
}

// DeleteFoodCategory 删除菜品分类
//
// @Tags 菜品分类
// @Security ApiAuthToken
// @Summary 删除菜品分类
// @Param id path int true "菜品分类ID"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodCategories/{id} [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:55"]
// @X-Version ["2.0"]
func (ctrl *FoodCategoryController) DeleteFoodCategory(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	err = ctrl.FoodCategoryService.Delete(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, nil)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, model.FoodModel{})
}

// UpdateFoodCategoryState 更新菜品分类状态
//
// @Tags 菜品分类
// @Security ApiAuthToken
// @Summary 更新菜品分类状态
// @Param id path int true "菜品分类ID"
// @Param request body request.UpdateFoodCategoryStateRequest true "更新菜品分类状态请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{ Data=resource.FoodCategoryResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foodCategories/{id}/state [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/26 16:55"]
// @X-Version ["2.0"]
func (ctrl *FoodCategoryController) UpdateFoodCategoryState(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	result, err := ctrl.FoodCategoryService.UpdateState(ctx, id, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodCategoryResource{}).Make(result))
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, model.FoodModel{})
}
