package util

import (
	"context"
	"github.com/samber/lo"
	"ros-api-go/internal/consts"
	"ros-api-go/pkg/i18n"
	"time"
)

func FormatDateTime(t *time.Time) *string {
	if t == nil || t.<PERSON>() {
		return nil
	}
	result := t.Format(time.DateTime)
	return &result
}

// ParseTimeWithZone 带时区转换
func ParseTimeWithZone(layout string, s string) (time.Time, error) {
	loc, err := time.LoadLocation("Local")
	if err != nil {
		return time.Time{}, err
	}
	t, err := time.ParseInLocation(layout, s, loc)
	if err != nil {
		return time.Time{}, err
	}
	return t, nil
}

func InArray(target string, arr []string) bool {
	for _, v := range arr {
		if v == target {
			return true
		}
	}
	return false
}

func IsEmpty(v any) bool {
	return v == nil || lo.IsEmpty(v)
}

// GetOfflinePaymentTradeType 获取线下支付交易类型
func GetOfflinePaymentTradeType(paymentType int64) string {
	switch paymentType {
	case consts.PAY_TYPE_PERSONAL_ALIPAY:
		return "PERSONAL_ALIPAY"
	case consts.PAY_TYPE_CASH:
		return "CASH"
	case consts.PAY_TYPE_UNIONPAY:
		return "UNIONPAY"
	case consts.PAY_TYPE_CANCEL:
		return "CANCEL"
	case consts.PAY_TYPE_MULAZIM:
		return "MULAZIM"
	case consts.PAY_TYPE_PERSONAL_WECHAT:
		return "PERSONAL_WECHAT"
	case consts.PAY_TYPE_YUNSHANFU:
		return "YUNSHANFU"
	default:
		return "UNKNOWN"
	}
}

// GetWeekday 获取星期几
func GetWeekday(ctx *context.Context, weekday time.Weekday) string {
	return i18n.Msg(ctx, "Days."+weekday.String())
}
